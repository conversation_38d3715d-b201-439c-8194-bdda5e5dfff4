<?php
// /api/delete_document.php

require_once '../config.php';
header('Content-Type: application/json');

// --- Optional token check ---
$headers = getallheaders();
$token = $headers['Authorization'] ?? '';

if ($token !== 'Bearer YOUR_API_TOKEN') {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Only POST allowed
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Read JSON
$data = json_decode(file_get_contents('php://input'), true);
$documentId = $data['document_id'] ?? null;

try {
    if (!$documentId) {
        throw new Exception('document_id is required');
    }

    // Fetch file path
    $stmt = $pdo->prepare("SELECT file_path FROM agent_documents WHERE document_id = ?");
    $stmt->execute([$documentId]);
    $document = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$document) {
        throw new Exception('Document not found');
    }

    if (!empty($document['file_path']) && file_exists($document['file_path'])) {
        unlink($document['file_path']);
    }

    // Delete record
    $stmt = $pdo->prepare("DELETE FROM agent_documents WHERE document_id = ?");
    $stmt->execute([$documentId]);

    echo json_encode(['success' => true]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
