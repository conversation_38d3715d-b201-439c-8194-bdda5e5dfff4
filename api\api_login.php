<?php
// api_login.php
require_once 'config.php';
require_once 'api_helpers.php';

// Only accept POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method Not Allowed']);
    exit;
}

// Get JSON payload
$data = json_decode(file_get_contents('php://input'), true);
$username = trim($data['username'] ?? '');
$password = trim($data['password'] ?? '');

if (empty($username) || empty($password)) {
    http_response_code(400);
    echo json_encode(['error' => 'Username and password are required']);
    exit;
}

$user = authenticateUser($username, $password);
if ($user) {
    // Generate API token
    $token = generateApiToken($user['user_id']);
    
    // Log login
    logActivity($user['user_id'], 'login', 'Mobile login');

    echo json_encode([
        'success' => true,
        'token' => $token,
        'user' => [
            'user_id' => $user['user_id'],
            'username' => $user['username'],
            'email' => $user['email']
        ]
    ]);
} else {
    http_response_code(401);
    echo json_encode(['error' => 'Invalid username or password']);
}
