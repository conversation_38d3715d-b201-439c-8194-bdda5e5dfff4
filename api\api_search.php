<?php
// api_search.php (for mobile API)

require_once 'config.php';
require_once 'api_helpers.php';

// Check token auth
$userId = authenticateBearerToken();
if (!$userId) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Get search query (GET param ?query=... or JSON body)
$query = $_GET['query'] ?? '';
$query = trim($query);

if (empty($query) || strlen($query) < 2) {
    echo json_encode([]);
    exit;
}

try {
    $results = [];

    // Search clients
    $clientSql = "SELECT
        client_id as id,
        name,
        email,
        phone_number,
        'client' as type
    FROM clients
    WHERE name LIKE :query
        OR email LIKE :query
        OR phone_number LIKE :query
        OR client_id LIKE :query
    LIMIT 5";

    $stmt = $pdo->prepare($clientSql);
    $stmt->execute([':query' => "%$query%"]);
    $clients = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Search agents
    $agentSql = "SELECT
        agent_id as id,
        name,
        email,
        phone_number,
        'agent' as type
    FROM agents
    WHERE name LIKE :query
        OR email LIKE :query
        OR phone_number LIKE :query
        OR agent_id LIKE :query
    LIMIT 5";

    $stmt = $pdo->prepare($agentSql);
    $stmt->execute([':query' => "%$query%"]);
    $agents = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Combine and sort
    $results = array_merge($clients, $agents);
    usort($results, fn($a, $b) => strcasecmp($a['name'], $b['name']));

    $results = array_slice($results, 0, 10);

    echo json_encode($results);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Search failed: ' . $e->getMessage()]);
}
