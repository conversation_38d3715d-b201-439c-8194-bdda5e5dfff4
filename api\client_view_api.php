<?php
session_start();
require_once 'config.php';

// Force JSON response
header('Content-Type: application/json');

// Check login
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'error' => 'Unauthorized. Please log in.'
    ]);
    exit;
}

// Check client_id
if (!isset($_GET['id']) || empty($_GET['id'])) {
    echo json_encode([
        'success' => false,
        'error' => 'Client ID is required.'
    ]);
    exit;
}

$clientId = $_GET['id'];

// Fetch client
$client = getClientById($clientId);
if (!$client) {
    echo json_encode([
        'success' => false,
        'error' => 'Client not found.'
    ]);
    exit;
}

// Get assigned agent info if any
$agentInfo = null;
if (!empty($client['agent_id'])) {
    $agentInfo = getAgentById($client['agent_id']);
}

// Get client's policies
$policies = getClientPolicies($clientId);

// Prepare policies array
$policiesArray = [];

if (!empty($policies)) {
    foreach ($policies as $policy) {
        // Get beneficiaries for this policy
        if (function_exists('getPolicyBeneficiaries')) {
            $beneficiaries = getPolicyBeneficiaries($policy['policy_id']);
        } else {
            // fallback direct query
            $beneficiariesQuery = "SELECT * FROM beneficiaries WHERE policy_id = ?";
            $stmt = $conn->prepare($beneficiariesQuery);
            $stmt->bind_param("s", $policy['policy_id']);
            $stmt->execute();
            $result = $stmt->get_result();
            $beneficiaries = [];
            while ($row = $result->fetch_assoc()) {
                $beneficiaries[] = $row;
            }
            $stmt->close();
        }

        $policiesArray[] = [
            'policy_id' => $policy['policy_id'],
            'plan_type' => $policy['plan_type'],
            'status' => $policy['status'],
            'agent_id' => $policy['agent_id'] ?? null,
            'agent_name' => $policy['agent_name'] ?? null,
            'sum_covered' => $policy['sum_covered'],
            'coverage_term' => $policy['coverage_term'],
            'contribution' => $policy['contribution'],
            'start_date' => $policy['start_date'],
            'end_date' => $policy['end_date'],
            'basic_plan_rider' => $policy['basic_plan_rider'] ?? null,
            'beneficiaries' => array_map(function($b) {
                return [
                    'name' => $b['name'],
                    'ic_number' => $b['ic_number'],
                    'relationship' => $b['relationship'],
                    'percentage' => $b['percentage']
                ];
            }, $beneficiaries),
        ];
    }
}

// Prepare client response
$response = [
    'success' => true,
    'client' => [
        'client_id' => $client['client_id'],
        'name' => $client['name'],
        'ic_number' => $client['ic_number'],
        'gender' => $client['gender'],
        'date_of_birth' => $client['date_of_birth'],
        'phone_number' => $client['phone_number'],
        'email' => $client['email'],
        'address' => $client['address'],
        'marital_status' => $client['marital_status'] ?? null,
        'race' => $client['race'] ?? null,
        'religion' => $client['religion'] ?? null,
        'nationality' => $client['nationality'] ?? null,
        'height' => $client['height'] ?? null,
        'weight' => $client['weight'] ?? null,
        'smoker' => $client['smoker'] ?? null,
        'occupation' => $client['occupation'] ?? null,
        'exact_duties' => $client['exact_duties'] ?? null,
        'nature_of_business' => $client['nature_of_business'] ?? null,
        'salary_yearly' => $client['salary_yearly'] ?? null,
        'company_name' => $client['company_name'] ?? null,
        'company_address' => $client['company_address'] ?? null,
    ],
    'assigned_agent' => $agentInfo,
    'policies' => $policiesArray,
];

echo json_encode($response);
exit;
