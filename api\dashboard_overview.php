<?php
// api/dashboard_overview.php

header('Content-Type: application/json');
require_once '../config.php';
require_once 'api_helpers.php';

// Check authentication with token
$token = getBearerToken();
$userId = validateApiToken($token);

if (!$userId) {
    http_response_code(401);
    echo json_encode([
        "success" => false,
        "message" => "Unauthorized"
    ]);
    exit;
}

try {
    $agentCount = $pdo->query("SELECT COUNT(*) FROM agents")->fetchColumn();
    $clientCount = $pdo->query("SELECT COUNT(*) FROM clients")->fetchColumn();
    $maleClients = $pdo->query("SELECT COUNT(*) FROM clients WHERE gender = 'Male'")->fetchColumn();
    $femaleClients = $pdo->query("SELECT COUNT(*) FROM clients WHERE gender = 'Female'")->fetchColumn();

    $recentAgents = $pdo->query("SELECT agent_id, name, email, phone_number 
                                 FROM agents ORDE<PERSON> BY created_at DESC LIMIT 5")->fetchAll(PDO::FETCH_ASSOC);
    
    $recentClients = $pdo->query("SELECT client_id, name, email, phone_number 
                                  FROM clients ORDER BY created_at DESC LIMIT 5")->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode([
        "success" => true,
        "data" => [
            "agent_count" => $agentCount,
            "client_count" => $clientCount,
            "male_clients" => $maleClients,
            "female_clients" => $femaleClients,
            "recent_agents" => $recentAgents,
            "recent_clients" => $recentClients
        ]
    ]);

} catch (Exception $e) {
    echo json_encode([
        "success" => false,
        "message" => $e->getMessage()
    ]);
}
