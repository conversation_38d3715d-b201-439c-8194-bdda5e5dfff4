<?php
// api/search.php

header('Content-Type: application/json');
require_once '../config.php';

// Optionally implement token-based auth here
// e.g. check a header or token parameter

$query = isset($_GET['q']) ? trim($_GET['q']) : '';

if (empty($query)) {
    echo json_encode([
        "success" => false,
        "message" => "Empty search query."
    ]);
    exit;
}

try {
    $clientSql = "
        SELECT 
            client_id AS id,
            name,
            email,
            phone_number,
            'client' AS type
        FROM clients
        WHERE name LIKE :query
        OR email LIKE :query
        OR phone_number LIKE :query
        OR client_id LIKE :query
        LIMIT 5
    ";
    $clientStmt = $pdo->prepare($clientSql);
    $clientStmt->execute([':query' => "%$query%"]);
    $clients = $clientStmt->fetchAll(PDO::FETCH_ASSOC);
    
    $agentSql = "
        SELECT 
            agent_id AS id,
            name,
            email,
            phone_number,
            'agent' AS type
        FROM agents
        WHERE name LIKE :query
        OR email LIKE :query
        OR phone_number LIKE :query
        OR agent_id LIKE :query
        LIMIT 5
    ";
    $agentStmt = $pdo->prepare($agentSql);
    $agentStmt->execute([':query' => "%$query%"]);
    $agents = $agentStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Combine & sort results
    $results = array_merge($clients, $agents);
    usort($results, function($a, $b) {
        return strcasecmp($a['name'], $b['name']);
    });
    
    $finalResults = array_slice($results, 0, 10);
    
    echo json_encode([
        "success" => true,
        "count" => count($finalResults),
        "results" => $finalResults
    ], JSON_PRETTY_PRINT);

} catch (Exception $e) {
    echo json_encode([
        "success" => false,
        "message" => $e->getMessage()
    ]);
}
