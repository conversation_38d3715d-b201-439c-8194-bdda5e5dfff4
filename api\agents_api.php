<?php
// agents_api.php

session_start();
require_once 'config.php';

// Cek user dah login ke belum (optional kalau app pakai token auth)
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        "success" => false,
        "message" => "Unauthorized"
    ]);
    exit;
}

// Handle sorting (sama macam web)
$sortColumn = isset($_GET['sort']) ? $_GET['sort'] : 'agent_id';
$sortOrder = isset($_GET['order']) ? $_GET['order'] : 'asc';

// Validate sort column
$allowedColumns = ['name', 'agent_id', 'gender'];
if (!in_array($sortColumn, $allowedColumns)) {
    $sortColumn = 'agent_id';
}

// Get all agents
$agents = getAllAgentsSorted($sortColumn, $sortOrder);

$data = [];

foreach ($agents as $agent) {
    $data[] = [
        'agent_id'      => $agent['agent_id'],
        'name'          => $agent['name'],
        'ic_number'     => $agent['ic_number'],
        'gender'        => $agent['gender'],
        'phone_number'  => $agent['phone_number'],
        'email'         => $agent['email'],
    ];
}

header('Content-Type: application/json');
echo json_encode([
    "success" => true,
    "agents" => $data
]);
