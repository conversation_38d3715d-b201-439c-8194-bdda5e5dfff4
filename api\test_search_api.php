<?php
header('Content-Type: application/json');
require_once '../config.php';

$query = $_GET['query'] ?? '';

if (!$query) {
    echo json_encode(['error' => 'No query provided']);
    exit;
}

try {
    $stmt = $pdo->prepare("SELECT client_id AS id, name, email, 'client' AS type FROM clients WHERE name LIKE :q
        UNION
        SELECT agent_id AS id, name, email, 'agent' AS type FROM agents WHERE name LIKE :q
        LIMIT 10");
    $stmt->execute([':q' => "%$query%"]);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode($results);
} catch (Exception $e) {
    echo json_encode(['error' => $e->getMessage()]);
}
