<?php
// api_logout.php
require_once 'config.php';
require_once 'api_helpers.php';

header('Content-Type: application/json');

// Only accept POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method Not Allowed']);
    exit;
}

// Check auth header
$token = getBearerToken();
$userId = validateApiToken($token);

if (!$userId) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Optionally: remove token from DB if you store them
// For simple systems, you might skip this step

// Log logout
logActivity($userId, 'logout', 'Mobile logout');

echo json_encode(['success' => true, 'message' => 'Logged out']);
