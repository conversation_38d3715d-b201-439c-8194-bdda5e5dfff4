<?php
// agent_view_api.php
session_start();
require_once 'config.php';

// Set JSON header
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized. Please log in.'
    ]);
    exit;
}

// Check if agent ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid agent ID.'
    ]);
    exit;
}

$agentId = $_GET['id'];

// Get agent info
$agent = getAgentById($agentId);
if (!$agent) {
    echo json_encode([
        'success' => false,
        'message' => 'Agent not found.'
    ]);
    exit;
}

// Get education details
$education = getAgentEducation($agentId);

// Get agent documents
$documents = getAgentDocuments($agentId);

// Get clients
$clients = getAgentClients($agentId);

// Prepare response
$response = [
    'success' => true,
    'agent' => $agent,
    'education' => $education,
    'clients' => $clients,
    'documents' => array_values($documents),
];

echo json_encode($response, JSON_PRETTY_PRINT);
exit;
?>
