<?php
require_once 'config.php';
require_once 'policies.php';

header('Content-Type: application/json');

// Simulate token check
$token = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
if ($token !== 'Bearer SECRET123') {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// collect POST data
$data = $_POST;
$errors = [];

// Validation
$name = trim($data['name'] ?? '');
$ic_number = preg_replace('/\D/', '', $data['ic_number'] ?? '');
$gender = $data['gender'] ?? '';
$date_of_birth = $data['date_of_birth'] ?? '';
$phone_number = preg_replace('/\D/', '', $data['phone_number'] ?? '');
$email = filter_var($data['email'] ?? '', FILTER_VALIDATE_EMAIL);
$address = trim($data['address'] ?? '');

// required fields
if (empty($name)) $errors[] = "Name is required";
if (empty($ic_number)) $errors[] = "IC Number is required";
if (empty($gender)) $errors[] = "Gender is required";
if (empty($date_of_birth)) $errors[] = "Date of birth is required";
if (empty($phone_number)) $errors[] = "Phone number is required";
if (empty($data['email'])) {
    $errors[] = "Email is required";
} elseif (!$email) {
    $errors[] = "Invalid email address";
}
if (empty($address)) $errors[] = "Address is required";

// Check duplicate client_id
$client_id = trim($data['client_id'] ?? '');
if (!empty($client_id) && clientIdExists($client_id)) {
    $errors[] = "Client ID already exists";
}

if (!empty($errors)) {
    echo json_encode(['success' => false, 'errors' => $errors]);
    exit;
}

try {
    $pdo->beginTransaction();

    // Generate pending ID if blank
    if (empty($client_id)) {
        $client_id = 'PENDING_' . time() . '_' . rand(1000, 9999);
    }

    $status = 'Pending';

    $stmt = $pdo->prepare("
        INSERT INTO clients (
            client_id, name, ic_number, gender, date_of_birth,
            phone_number, email, address, status
        ) VALUES (
            :client_id, :name, :ic_number, :gender, :date_of_birth,
            :phone_number, :email, :address, :status
        )
    ");
    $stmt->execute([
        ':client_id' => $client_id,
        ':name' => $name,
        ':ic_number' => $ic_number,
        ':gender' => $gender,
        ':date_of_birth' => $date_of_birth,
        ':phone_number' => $phone_number,
        ':email' => $email,
        ':address' => $address,
        ':status' => $status
    ]);

    $pdo->commit();

    echo json_encode([
        'success' => true,
        'message' => 'Client added successfully',
        'client_id' => $client_id
    ]);

} catch (Exception $e) {
    $pdo->rollBack();
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
