<?php
require_once '../config.php';

header('Content-Type: application/json');

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Only POST requests allowed']);
    exit;
}

// Get agent ID from query string
$agentId = $_GET['id'] ?? null;

if (!$agentId) {
    http_response_code(400);
    echo json_encode(['error' => 'Agent ID is required in URL']);
    exit;
}

// Check if agent exists
$agent = getAgentById($agentId);
if (!$agent) {
    http_response_code(404);
    echo json_encode(['error' => 'Agent not found']);
    exit;
}

try {
    $data = [];

    // If JSON payload:
    if (stripos($_SERVER['CONTENT_TYPE'] ?? '', 'application/json') !== false) {
        $payload = json_decode(file_get_contents('php://input'), true);
        $data = [
            'agent_id' => trim($payload['agent_id'] ?? $agentId),
            'name' => trim($payload['name'] ?? ''),
            'ic_number' => preg_replace('/[^0-9]/', '', $payload['ic_number'] ?? ''),
            'gender' => trim($payload['gender'] ?? ''),
            'date_of_birth' => trim($payload['date_of_birth'] ?? ''),
            'phone_number' => preg_replace('/[^0-9]/', '', $payload['phone_number'] ?? ''),
            'email' => trim($payload['email'] ?? ''),
            'address' => trim($payload['address'] ?? ''),
            'beneficiary_phone' => preg_replace('/[^0-9]/', '', $payload['beneficiary_phone'] ?? ''),
            'work_experience' => trim($payload['work_experience'] ?? '')
        ];
    } else {
        // If multipart/form-data payload:
        $data = [
            'agent_id' => trim($_POST['agent_id'] ?? $agentId),
            'name' => trim($_POST['name'] ?? ''),
            'ic_number' => preg_replace('/[^0-9]/', '', $_POST['ic_number'] ?? ''),
            'gender' => trim($_POST['gender'] ?? ''),
            'date_of_birth' => trim($_POST['date_of_birth'] ?? ''),
            'phone_number' => preg_replace('/[^0-9]/', '', $_POST['phone_number'] ?? ''),
            'email' => trim($_POST['email'] ?? ''),
            'address' => trim($_POST['address'] ?? ''),
            'beneficiary_phone' => preg_replace('/[^0-9]/', '', $_POST['beneficiary_phone'] ?? ''),
            'work_experience' => trim($_POST['work_experience'] ?? '')
        ];
    }

    // Validate required fields
    $errors = [];
    if (empty($data['agent_id'])) $errors[] = 'Agent ID cannot be empty';
    if (empty($data['name'])) $errors[] = 'Name cannot be empty';
    if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) $errors[] = 'Invalid email format';

    // Check duplicate agent ID if changed
    if ($data['agent_id'] !== $agentId && agentIdExists($data['agent_id'])) {
        $errors[] = "Agent ID '{$data['agent_id']}' already exists";
    }

    if (!empty($errors)) {
        http_response_code(400);
        echo json_encode(['errors' => $errors]);
        exit;
    }

    // Handle photo upload
    if (isset($_FILES['photo']) && $_FILES['photo']['error'] === UPLOAD_ERR_OK) {
        $uploadDir = '../uploads/photos/';
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }

        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
        $fileType = $_FILES['photo']['type'];
        if (!in_array($fileType, $allowedTypes)) {
            throw new Exception('Invalid photo type. Only JPG, PNG, GIF allowed.');
        }

        $ext = strtolower(pathinfo($_FILES['photo']['name'], PATHINFO_EXTENSION));
        $newFileName = 'photo_' . $agentId . '_' . time() . '.' . $ext;
        $targetPath = $uploadDir . $newFileName;

        // Delete old photo
        if (!empty($agent['photo']) && file_exists('../' . $agent['photo'])) {
            unlink('../' . $agent['photo']);
        }

        if (move_uploaded_file($_FILES['photo']['tmp_name'], $targetPath)) {
            $data['photo'] = substr($targetPath, 3); // save relative path
        } else {
            throw new Exception('Failed to upload photo');
        }
    }

    // Handle document uploads
    $documentTypes = [
        'education_cert' => 1,
        'nric_copy' => 2,
        'beneficiary_nric' => 3
    ];

    foreach ($documentTypes as $docType => $typeId) {
        if (isset($_FILES[$docType]) && $_FILES[$docType]['error'] === UPLOAD_ERR_OK) {
            $uploadDir = '../uploads/documents/';
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }

            $allowedTypes = ['application/pdf'];
            $fileType = $_FILES[$docType]['type'];
            if (!in_array($fileType, $allowedTypes)) {
                throw new Exception("Invalid file type for {$docType}. Only PDF allowed.");
            }

            $ext = strtolower(pathinfo($_FILES[$docType]['name'], PATHINFO_EXTENSION));
            $newFileName = $docType . '_' . $agentId . '_' . time() . '.' . $ext;
            $targetPath = $uploadDir . $newFileName;

            if (move_uploaded_file($_FILES[$docType]['tmp_name'], $targetPath)) {
                // Delete old record
                $stmt = $pdo->prepare("DELETE FROM agent_documents WHERE agent_id = ? AND document_type_id = ?");
                $stmt->execute([$agentId, $typeId]);

                $stmt = $pdo->prepare("
                    INSERT INTO agent_documents (agent_id, document_type_id, file_name, file_path, file_size)
                    VALUES (?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $agentId,
                    $typeId,
                    $_FILES[$docType]['name'],
                    substr($targetPath, 3), // save relative path
                    $_FILES[$docType]['size']
                ]);
            } else {
                throw new Exception("Failed to upload {$docType}");
            }
        }
    }

    // Update agent
    $success = updateAgent($agentId, $data);
    if ($success) {
        http_response_code(200);
        echo json_encode([
            'success' => true,
            'message' => 'Agent updated successfully',
            'agent_id' => $data['agent_id']
        ]);
    } else {
        throw new Exception('Failed to update agent in database');
    }

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}
