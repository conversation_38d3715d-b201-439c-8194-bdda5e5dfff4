<?php
// api/get_all_data.php

header('Content-Type: application/json');
require_once '../config.php';

// Check authentication
session_start();
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized'
    ]);
    exit;
}

try {
    $allData = [];

    // Get all agents with their details
    $agentStmt = $pdo->query("
        SELECT a.*, 
               COUNT(DISTINCT p.policy_id) as total_policies,
               COUNT(DISTINCT c.client_id) as total_clients
        FROM agents a
        LEFT JOIN policies p ON a.agent_id = p.agent_id
        LEFT JOIN clients c ON p.client_id = c.client_id
        GROUP BY a.agent_id
        ORDER BY a.name ASC
    ");
    $allData['agents'] = $agentStmt->fetchAll(PDO::FETCH_ASSOC);

    // Get all clients with policy counts
    $clientStmt = $pdo->query("
        SELECT c.*,
               COUNT(p.policy_id) as total_policies,
               SUM(CASE WHEN p.status = 'Active' THEN 1 ELSE 0 END) as active_policies,
               SUM(CASE WHEN p.status = 'Pending' THEN 1 ELSE 0 END) as pending_policies
        FROM clients c
        LEFT JOIN policies p ON c.client_id = p.client_id
        GROUP BY c.client_id
        ORDER BY c.name ASC
    ");
    $allData['clients'] = $clientStmt->fetchAll(PDO::FETCH_ASSOC);

    // Get all policies with agent and client details
    $policyStmt = $pdo->query("
        SELECT p.*, 
               a.name as agent_name,
               c.name as client_name
        FROM policies p
        LEFT JOIN agents a ON p.agent_id = a.agent_id
        LEFT JOIN clients c ON p.client_id = c.client_id
        ORDER BY p.created_at DESC
    ");
    $allData['policies'] = $policyStmt->fetchAll(PDO::FETCH_ASSOC);

    // Get all beneficiaries
    $beneficiaryStmt = $pdo->query("
        SELECT b.*, p.client_id, c.name as client_name
        FROM beneficiaries b
        LEFT JOIN policies p ON b.policy_id = p.policy_id
        LEFT JOIN clients c ON p.client_id = c.client_id
        ORDER BY b.name ASC
    ");
    $allData['beneficiaries'] = $beneficiaryStmt->fetchAll(PDO::FETCH_ASSOC);

    // Get all users
    $userStmt = $pdo->query("
        SELECT user_id, username, email, created_at
        FROM users
        ORDER BY created_at DESC
    ");
    $allData['users'] = $userStmt->fetchAll(PDO::FETCH_ASSOC);

    // Get agent education records
    $educationStmt = $pdo->query("
        SELECT ae.*, a.name as agent_name
        FROM agent_education ae
        LEFT JOIN agents a ON ae.agent_id = a.agent_id
        ORDER BY ae.agent_id, ae.year DESC
    ");
    $allData['agent_education'] = $educationStmt->fetchAll(PDO::FETCH_ASSOC);

    // Get agent documents if table exists
    $tableCheck = $pdo->query("SHOW TABLES LIKE 'agent_documents'");
    if ($tableCheck->rowCount() > 0) {
        $docStmt = $pdo->query("
            SELECT ad.*, a.name as agent_name
            FROM agent_documents ad
            LEFT JOIN agents a ON ad.agent_id = a.agent_id
            ORDER BY ad.agent_id, ad.uploaded_at DESC
        ");
        $allData['agent_documents'] = $docStmt->fetchAll(PDO::FETCH_ASSOC);
    } else {
        $allData['agent_documents'] = [];
    }

    // Get client documents if table exists
    $clientDocCheck = $pdo->query("SHOW TABLES LIKE 'client_documents'");
    if ($clientDocCheck->rowCount() > 0) {
        $clientDocStmt = $pdo->query("
            SELECT cd.*, c.name as client_name
            FROM client_documents cd
            LEFT JOIN clients c ON cd.client_id = c.client_id
            ORDER BY cd.client_id, cd.uploaded_at DESC
        ");
        $allData['client_documents'] = $clientDocStmt->fetchAll(PDO::FETCH_ASSOC);
    } else {
        $allData['client_documents'] = [];
    }

    // Get API logs if table exists
    $apiLogCheck = $pdo->query("SHOW TABLES LIKE 'api_logs'");
    if ($apiLogCheck->rowCount() > 0) {
        $apiLogStmt = $pdo->query("
            SELECT * FROM api_logs
            ORDER BY created_at DESC
            LIMIT 100
        ");
        $allData['api_logs'] = $apiLogStmt->fetchAll(PDO::FETCH_ASSOC);
    } else {
        $allData['api_logs'] = [];
    }

    // Get summary statistics
    $allData['statistics'] = [
        'total_agents' => count($allData['agents']),
        'total_clients' => count($allData['clients']),
        'total_policies' => count($allData['policies']),
        'total_beneficiaries' => count($allData['beneficiaries']),
        'total_users' => count($allData['users']),
        'active_policies' => array_sum(array_column($allData['clients'], 'active_policies')),
        'pending_policies' => array_sum(array_column($allData['clients'], 'pending_policies')),
        'male_clients' => count(array_filter($allData['clients'], fn($c) => $c['gender'] === 'Male')),
        'female_clients' => count(array_filter($allData['clients'], fn($c) => $c['gender'] === 'Female'))
    ];

    echo json_encode([
        'success' => true,
        'timestamp' => date('Y-m-d H:i:s'),
        'data' => $allData
    ], JSON_PRETTY_PRINT);

} catch (Exception $e) {
    error_log("Error in get_all_data.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>