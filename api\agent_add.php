<?php
// API: agent_add.php
require_once '../config.php';
header('Content-Type: application/json');

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Only POST requests are allowed']);
    exit;
}

// Get JSON input
$data = json_decode(file_get_contents('php://input'), true);

// Validate and sanitize
$agent_id = trim($data['agent_id'] ?? '');
$name = trim($data['name'] ?? '');
$ic_number = preg_replace('/[^0-9]/', '', $data['ic_number'] ?? '');
$gender = $data['gender'] ?? '';
$date_of_birth = $data['date_of_birth'] ?? '';
$phone_number = preg_replace('/[^0-9]/', '', $data['phone_number'] ?? '');
$email = filter_var($data['email'] ?? '', FILTER_VALIDATE_EMAIL);
$address = trim($data['address'] ?? '');
$beneficiary_phone = preg_replace('/[^0-9]/', '', $data['beneficiary_phone'] ?? '');
$beneficiary_name = trim($data['beneficiary_name'] ?? '');
$beneficiary_ic = preg_replace('/[^0-9]/', '', $data['beneficiary_ic'] ?? '');
$work_experience = trim($data['work_experience'] ?? '');

$errors = [];

if (!$agent_id || !preg_match('/^[A-Za-z0-9]+$/', $agent_id)) {
    $errors[] = 'Agent ID is required and must be alphanumeric.';
}
if (!$name) $errors[] = 'Name is required.';
if (!$ic_number) $errors[] = 'IC number is required.';
if (!in_array($gender, ['Male', 'Female'])) $errors[] = 'Valid gender is required.';
if (!$date_of_birth) $errors[] = 'Date of birth is required.';
if (!$phone_number) $errors[] = 'Phone number is required.';
if (!$email) $errors[] = 'Valid email is required.';
if (!$address) $errors[] = 'Address is required.';

// Check duplicate agent ID
if (empty($errors) && checkAgentIdExists($agent_id, $conn)) {
    $errors[] = "Agent ID already exists.";
}

if (!empty($errors)) {
    http_response_code(400);
    echo json_encode(['errors' => $errors]);
    exit;
}

// Prepare data
$agentData = [
    'agent_id' => $agent_id,
    'name' => $name,
    'ic_number' => $ic_number,
    'gender' => $gender,
    'date_of_birth' => $date_of_birth,
    'phone_number' => $phone_number,
    'email' => $email,
    'address' => $address,
    'beneficiary_phone' => $beneficiary_phone,
    'beneficiary_name' => $beneficiary_name,
    'beneficiary_ic' => $beneficiary_ic,
    'work_experience' => $work_experience,
    'photo' => null // Not handled yet
];

$educationData = $data['education'] ?? []; // Assume it's an array of { level, year, institution }

// Insert to DB
if (createNewAgent($agentData, $educationData, $conn)) {
    echo json_encode(['success' => true, 'message' => 'Agent created successfully']);
} else {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to add agent']);
}
