<?php
// api/clients.php

header('Content-Type: application/json');
require_once '../config.php';

// Check for authentication (optional for now)
session_start();
if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized'
    ]);
    exit;
}

// Use same function as in client.php, just move it here:
function getClientsWithPolicyData($conn) {
    $clients = [];

    $queryClients = "SELECT * FROM clients";
    $resultClients = mysqli_query($conn, $queryClients);

    if (!$resultClients) {
        return [
            'success' => false,
            'message' => 'Database query error: ' . mysqli_error($conn)
        ];
    }

    while ($client = mysqli_fetch_assoc($resultClients)) {
        $policyQuery = "
            SELECT policy_id, status
            FROM policies
            WHERE client_id = '" . mysqli_real_escape_string($conn, $client['client_id']) . "'";

        $policyResult = mysqli_query($conn, $policyQuery);

        if (!$policyResult) {
            return [
                'success' => false,
                'message' => 'Database query error: ' . mysqli_error($conn)
            ];
        }

        $client['total_policies'] = 0;
        $client['active_policies'] = 0;
        $client['pending_policies'] = 0;

        while ($policy = mysqli_fetch_assoc($policyResult)) {
            $client['total_policies']++;

            $status = trim($policy['status']);
            if (strcasecmp($status, 'Active') === 0) {
                $client['active_policies']++;
            } elseif (strcasecmp($status, 'Pending') === 0) {
                $client['pending_policies']++;
            }
        }

        $clients[] = $client;
    }

    return [
        'success' => true,
        'clients' => $clients
    ];
}

// Run it:
$response = getClientsWithPolicyData($conn);

// Return JSON
echo json_encode($response);
