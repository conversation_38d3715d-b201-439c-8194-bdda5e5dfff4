<?php
require_once 'config.php';

function generateApiToken($userId) {
    $payload = [
        'user_id' => $userId,
        'iat' => time(),
        'exp' => time() + 86400,
        'random' => bin2hex(random_bytes(16))
    ];
    
    $secret = JWT_SECRET;
    $header = base64_encode(json_encode(['typ' => 'JWT', 'alg' => 'HS256']));
    $payload_encoded = base64_encode(json_encode($payload));
    $signature = hash_hmac('sha256', $header . '.' . $payload_encoded, $secret, true);
    $signature_encoded = base64_encode($signature);
    
    return $header . '.' . $payload_encoded . '.' . $signature_encoded;
}

function validateApiToken($token) {
    if (!$token) return false;
    
    $parts = explode('.', $token);
    if (count($parts) !== 3) return false;
    
    $secret = JWT_SECRET;
    $header = $parts[0];
    $payload = $parts[1];
    $signature = $parts[2];
    
    // Verify signature
    $expected_signature = base64_encode(hash_hmac('sha256', $header . '.' . $payload, $secret, true));
    if ($signature !== $expected_signature) return false;
    
    // Decode payload
    $payload_data = json_decode(base64_decode($payload), true);
    if (!$payload_data || $payload_data['exp'] < time()) return false;
    
    return $payload_data['user_id'];
}

function getBearerToken() {
    $headers = getallheaders();
    
    if (isset($headers['Authorization'])) {
        $auth = $headers['Authorization'];
        if (preg_match('/Bearer\s+(.*)$/i', $auth, $matches)) {
            return $matches[1];
        }
    }
    
    return null;
}

function authenticateBearerToken() {
    $token = getBearerToken();
    return validateApiToken($token);
}
