<?php
session_start();

require_once 'config.php';
require_once 'policies.php';

// Return JSON response
header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized'
    ]);
    exit;
}

// Read raw JSON body
$json = file_get_contents('php://input');
$data = json_decode($json, true);

if (json_last_error() !== JSON_ERROR_NONE) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Invalid JSON payload'
    ]);
    exit;
}

// Validate payload
$errors = validatePolicyPayload($data);
if (!empty($errors)) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Validation failed',
        'errors' => $errors
    ]);
    exit;
}

try {
    // Add client_id and default status
    $data['status'] = 'Pending';

    // Insert policy
    $policyId = pm_addPolicy($data);

    if ($policyId) {
        // Handle beneficiary if provided
        if (!empty($data['beneficiary'])) {
            $beneficiary = $data['beneficiary'];

            // Only insert if at least name and ic_number are provided
            if (!empty($beneficiary['name']) && !empty($beneficiary['ic_number'])) {
                $sql = "INSERT INTO beneficiaries (
                    policy_id, name, ic_number, relationship, date_of_birth,
                    phone_number, email, address, percentage
                ) VALUES (
                    :policy_id, :name, :ic_number, :relationship, :date_of_birth,
                    :phone_number, :email, :address, :percentage
                )";

                $stmt = $pdo->prepare($sql);
                $stmt->execute([
                    ':policy_id' => $policyId,
                    ':name' => $beneficiary['name'],
                    ':ic_number' => $beneficiary['ic_number'],
                    ':relationship' => $beneficiary['relationship'] ?? '',
                    ':date_of_birth' => $beneficiary['date_of_birth'] ?? null,
                    ':phone_number' => $beneficiary['phone_number'] ?? '',
                    ':email' => $beneficiary['email'] ?? '',
                    ':address' => $beneficiary['address'] ?? '',
                    ':percentage' => $beneficiary['percentage'] ?? 100.00
                ]);
            }
        }

        echo json_encode([
            'success' => true,
            'message' => 'Policy created successfully',
            'policy_id' => $policyId
        ]);
    } else {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Failed to add policy'
        ]);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
    error_log("Error in api_create_policy.php: " . $e->getMessage());
}

/**
 * Validate policy payload
 */
function validatePolicyPayload($data) {
    $errors = [];

    if (empty($data['client_id'])) {
        $errors[] = 'client_id is required';
    }
    if (empty($data['agent_id'])) {
        $errors[] = 'agent_id is required';
    }
    if (empty($data['plan_type'])) {
        $errors[] = 'plan_type is required';
    }
    if (empty($data['policy_id'])) {
        $errors[] = 'policy_id is required';
    }
    if (empty($data['basic_plan_rider'])) {
        $errors[] = 'basic_plan_rider is required';
    }
    if (!isset($data['sum_covered'])) {
        $errors[] = 'sum_covered is required';
    }
    if (empty($data['coverage_term'])) {
        $errors[] = 'coverage_term is required';
    }
    if (!isset($data['contribution'])) {
        $errors[] = 'contribution is required';
    }
    if (empty($data['start_date'])) {
        $errors[] = 'start_date is required';
    }
    if (empty($data['end_date'])) {
        $errors[] = 'end_date is required';
    }

    return $errors;
}
