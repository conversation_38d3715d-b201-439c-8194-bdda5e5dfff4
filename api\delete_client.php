<?php
// /api/delete_client.php

require_once '../config.php';
header('Content-Type: application/json');

// --- Optional Token Check ---
// e.g. check token in headers:
$headers = getallheaders();
$token = $headers['Authorization'] ?? '';

if ($token !== 'Bearer YOUR_API_TOKEN') {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized'
    ]);
    exit;
}

// Only allow POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Read JSON input
$postData = json_decode(file_get_contents('php://input'), true);
$clientId = $postData['client_id'] ?? null;

if (!$clientId) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'client_id is required']);
    exit;
}

try {
    $stmt = $pdo->prepare("SELECT name FROM clients WHERE client_id = ?");
    $stmt->execute([$clientId]);
    $client = $stmt->fetch();

    if (!$client) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Client not found']);
        exit;
    }

    $pdo->beginTransaction();

    // Delete policies
    $stmt = $pdo->prepare("DELETE FROM policies WHERE client_id = ?");
    $stmt->execute([$clientId]);

    // Delete client_documents if table exists
    $tableCheck = $pdo->query("SHOW TABLES LIKE 'client_documents'");
    if ($tableCheck->rowCount() > 0) {
        $stmt = $pdo->prepare("DELETE FROM client_documents WHERE client_id = ?");
        $stmt->execute([$clientId]);
    }

    // Delete client
    $stmt = $pdo->prepare("DELETE FROM clients WHERE client_id = ?");
    $stmt->execute([$clientId]);

    $pdo->commit();

    // Optionally log deletion (if you want to store API calls separately)
    $stmt = $pdo->prepare("
        INSERT INTO api_logs (endpoint, payload, result, created_at)
        VALUES (?, ?, ?, NOW())
    ");
    $stmt->execute([
        '/api/delete_client.php',
        json_encode($postData),
        'Deleted client: ' . $client['name']
    ]);

    http_response_code(200);
    echo json_encode(['success' => true]);

} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    error_log("API ERROR delete_client: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Internal Server Error']);
}
exit;
