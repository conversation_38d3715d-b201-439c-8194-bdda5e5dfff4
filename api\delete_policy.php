<?php
// /api/delete_policy.php

require_once '../config.php';

header('Content-Type: application/json');

// Check auth token
$headers = getallheaders();
$token = $headers['Authorization'] ?? '';

if ($token !== 'Bearer YOUR_API_TOKEN') {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Only allow POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Parse JSON
$data = json_decode(file_get_contents('php://input'), true);
$policyId = $data['policy_id'] ?? null;

if (!$policyId) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'policy_id is required']);
    exit;
}

try {
    $pdo->beginTransaction();

    // Check if policy_beneficiaries table exists
    $tableExists = false;
    $checkStmt = $pdo->query("SHOW TABLES LIKE 'policy_beneficiaries'");
    if ($checkStmt->rowCount() > 0) {
        $tableExists = true;
    }

    // Delete beneficiaries if table exists
    if ($tableExists) {
        $stmt = $pdo->prepare("DELETE FROM policy_beneficiaries WHERE policy_id = ?");
        $stmt->execute([$policyId]);
    }

    // Delete the policy
    $stmt = $pdo->prepare("DELETE FROM policies WHERE policy_id = ?");
    $stmt->execute([$policyId]);

    if ($stmt->rowCount() > 0) {
        $pdo->commit();
        echo json_encode(['success' => true]);
    } else {
        $pdo->rollBack();
        throw new Exception('Policy not found or could not be deleted');
    }

} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
    ]);
}
