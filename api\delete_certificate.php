<?php
// /api/delete_certificate.php

header('Content-Type: application/json');
require_once '../config.php';

// Optional: check token auth here instead of session
// e.g. check a token in headers:
// $headers = getallheaders();
// $token = $headers['Authorization'] ?? '';
// if ($token !== 'YOUR_APP_TOKEN') { ... }

$agentId = isset($_GET['agent_id']) ? trim($_GET['agent_id']) : '';

if (empty($agentId)) {
    echo json_encode([
        'success' => false,
        'message' => 'Missing agent_id'
    ]);
    exit;
}

// Lookup agent
$agent = getAgentById($agentId);
if (!$agent) {
    echo json_encode([
        'success' => false,
        'message' => 'Agent not found'
    ]);
    exit;
}

$deleted = false;

// Delete the file if it exists
if (!empty($agent['education_cert']) && file_exists($agent['education_cert'])) {
    if (unlink($agent['education_cert'])) {
        $deleted = true;
    }
}

// Update DB
$data = $agent;
$data['education_cert'] = null;
$updateSuccess = updateAgent($agentId, $data);

if ($updateSuccess || $deleted) {
    echo json_encode([
        'success' => true,
        'message' => 'Certificate deleted'
    ]);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Failed to delete certificate'
    ]);
}
exit;
