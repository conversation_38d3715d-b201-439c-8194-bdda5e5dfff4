<?php
require_once 'config.php';
require_once 'policies.php';

header('Content-Type: application/json');

// Simulate token check
$token = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
if ($token !== 'Bearer SECRET123') {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized'
    ]);
    exit;
}

// Check client ID — allow either GET or POST
$clientId = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $clientId = $_POST['id'] ?? null;
} elseif ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $clientId = $_GET['id'] ?? null;
}

if (empty($clientId) || !is_numeric($clientId)) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Invalid client ID'
    ]);
    exit;
}

$clientId = (int) $clientId;

try {
    // Check if client exists
    $stmt = $pdo->prepare("SELECT name FROM clients WHERE id = ?");
    $stmt->execute([$clientId]);
    $client = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$client) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => 'Client not found'
        ]);
        exit;
    }

    // Delete the client
    $stmt = $pdo->prepare("DELETE FROM clients WHERE id = ?");
    $stmt->execute([$clientId]);

    // Log the activity
    // Replace 123 below with actual user ID from your token logic
    logActivity(123, 'Deleted client', "Deleted client: {$client['name']}");

    echo json_encode([
        'success' => true,
        'message' => "Client deleted successfully"
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error deleting client: ' . $e->getMessage()
    ]);
}
