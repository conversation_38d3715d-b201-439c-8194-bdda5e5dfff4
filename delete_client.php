<?php
session_start();
require_once 'config.php';

header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Get the client ID from POST data (from JSON)
$postData = json_decode(file_get_contents('php://input'), true);
$clientId = $postData['client_id'] ?? null;

if (!$clientId) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Client ID is required']);
    exit;
}

try {
    // First get client details for logging
    $stmt = $pdo->prepare("SELECT name FROM clients WHERE client_id = ?");
    $stmt->execute([$clientId]);
    $client = $stmt->fetch();
    
    if (!$client) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Client not found']);
        exit;
    }
    
    // Start transaction
    $pdo->beginTransaction();
    
    // 1. Delete from policies first
    $stmt = $pdo->prepare("DELETE FROM policies WHERE client_id = ?");
    $stmt->execute([$clientId]);
    
    // 2. Delete from client_documents if table exists
    $tableCheck = $pdo->query("SHOW TABLES LIKE 'client_documents'");
    if ($tableCheck->rowCount() > 0) {
        $stmt = $pdo->prepare("DELETE FROM client_documents WHERE client_id = ?");
        $stmt->execute([$clientId]);
    }
    
    // 3. Finally delete from clients table
    $stmt = $pdo->prepare("DELETE FROM clients WHERE client_id = ?");
    $stmt->execute([$clientId]);
    
    // Commit transaction
    $pdo->commit();
    
    // Log the activity
    if (isset($_SESSION['user_id']) && is_numeric($_SESSION['user_id'])) {
        $stmt = $pdo->prepare("INSERT INTO activity_logs (user_id, action, details, ip_address) 
                              VALUES (?, ?, ?, ?)");
        $stmt->execute([
            $_SESSION['user_id'],
            'delete_client',
            "Deleted client: {$client['name']} (ID: {$clientId})",
            $_SERVER['REMOTE_ADDR'] ?? null
        ]);
    }
    
    http_response_code(200);
    echo json_encode(['success' => true]);
} catch (Exception $e) {
    // Rollback on error
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    error_log("Error deleting client: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'An error occurred while deleting the client']);
}
exit;
?> 