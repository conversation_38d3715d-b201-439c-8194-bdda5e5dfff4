<?php
// update_schema_secure.php - Secure version of schema update script

require_once 'config.php';

// --- SECURITY: Require a secret token to proceed ---
$secret_token = 'yourSuperSecretToken123'; // Ganti ini dengan token sebenar

// Check if token is passed and matches
if (!isset($_GET['token']) || $_GET['token'] !== $secret_token) {
    http_response_code(403);
    echo "Access denied. Invalid or missing token.";
    exit;
}

// Start output buffer to capture any errors
ob_start();

try {
    // Update agents table
    $pdo->exec("ALTER TABLE agents MODIFY COLUMN agent_id VARCHAR(20)");
    echo "✓ Updated agents table schema<br>";

    // Update agent_client table
    $pdo->exec("ALTER TABLE agent_client MODIFY COLUMN agent_id VARCHAR(20)");
    echo "✓ Updated agent_client table schema<br>";

    // Update educationdetails table
    $pdo->exec("ALTER TABLE educationdetails MODIFY COLUMN agent_id VARCHAR(20)");
    echo "✓ Updated educationdetails table schema<br>";

    // Update agent_documents table
    $pdo->exec("ALTER TABLE agent_documents MODIFY COLUMN agent_id VARCHAR(20)");
    echo "✓ Updated agent_documents table schema<br>";

    echo "<p style='color:green;font-weight:bold;'>Schema update completed successfully!</p>";
    echo "<p>Your database now supports alphanumeric agent IDs.</p>";

} catch (PDOException $e) {
    echo "<p style='color:red;font-weight:bold;'>Error updating schema: " . $e->getMessage() . "</p>";
    echo "<p>Last SQL query: " . $pdo->errorInfo()[2] . "</p>";
}

$output = ob_get_clean();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Secure Schema Update</title>
</head>
<body>
    <h1>Secure Database Schema Update</h1>
    <?php echo $output; ?>
</body>
</html>
