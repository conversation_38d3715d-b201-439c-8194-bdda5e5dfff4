<?php
// api/get_policy_form_data.php

require_once '../config.php';
require_once '../policies.php';

header('Content-Type: application/json');

// Authenticate using API token
$headers = getallheaders();
$token = $headers['Authorization'] ?? '';

if ($token !== 'Bearer YOUR_API_TOKEN') {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Get index parameter (optional)
$index = isset($_GET['index']) ? intval($_GET['index']) : 0;

try {
    // Get all agents for dropdown
    $agents = getAllAgents(); // Assumes this returns an array of ['agent_id' => ..., 'name' => ...]

    $agentOptions = array_map(function ($agent) {
        return [
            'id' => $agent['agent_id'],
            'name' => $agent['name']
        ];
    }, $agents);

    // Define fields expected in the policy form
    $fields = [
        ['name' => 'policy_number', 'type' => 'text', 'label' => 'Policy Number'],
        ['name' => 'agent_id', 'type' => 'select', 'label' => 'Agent', 'options' => $agentOptions],
        ['name' => 'start_date', 'type' => 'date', 'label' => 'Start Date'],
        ['name' => 'end_date', 'type' => 'date', 'label' => 'End Date'],
        ['name' => 'premium', 'type' => 'number', 'label' => 'Premium'],
        ['name' => 'status', 'type' => 'select', 'label' => 'Status', 'options' => [
            ['id' => 'Active', 'name' => 'Active'],
            ['id' => 'Pending', 'name' => 'Pending'],
            ['id' => 'Lapse', 'name' => 'Lapse'],
        ]],
    ];

    echo json_encode([
        'success' => true,
        'index' => $index,
        'fields' => $fields
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
