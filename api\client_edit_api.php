<?php
require_once 'config.php';
require_once 'policies.php';

header('Content-Type: application/json');

// Auth check (dummy token for now)
$token = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
if ($token !== 'Bearer SECRET123') {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized'
    ]);
    exit;
}

// Get client ID
$clientId = $_POST['client_id'] ?? null;
if (empty($clientId) || !is_numeric($clientId)) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Invalid client ID'
    ]);
    exit;
}

// Validate required fields
$requiredFields = ['name', 'ic_number', 'gender', 'date_of_birth', 'phone_number', 'email', 'address'];
$errors = [];

foreach ($requiredFields as $field) {
    if (empty($_POST[$field])) {
        $errors[] = ucfirst(str_replace('_', ' ', $field)) . ' is required';
    }
}

if (!empty($errors)) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Validation errors',
        'errors' => $errors
    ]);
    exit;
}

try {
    $pdo->beginTransaction();

    // Update clients table
    $sql = "UPDATE clients SET 
        name = :name,
        ic_number = :ic_number,
        client_number = :client_number,
        gender = :gender,
        date_of_birth = :date_of_birth,
        phone_number = :phone_number,
        email = :email,
        address = :address,
        marital_status = :marital_status,
        race = :race,
        religion = :religion,
        nationality = :nationality,
        occupation = :occupation,
        exact_duties = :exact_duties,
        nature_of_business = :nature_of_business,
        salary_yearly = :salary_yearly,
        company_name = :company_name,
        company_address = :company_address,
        weight = :weight,
        height = :height,
        smoker = :smoker,
        hospital_admission_history = :hospital_admission_history
        WHERE client_id = :client_id";

    $stmt = $pdo->prepare($sql);
    $stmt->execute([
        ':name' => $_POST['name'],
        ':ic_number' => $_POST['ic_number'],
        ':client_number' => $_POST['client_number'] ?? null,
        ':gender' => $_POST['gender'],
        ':date_of_birth' => $_POST['date_of_birth'],
        ':phone_number' => $_POST['phone_number'],
        ':email' => $_POST['email'],
        ':address' => $_POST['address'],
        ':marital_status' => $_POST['marital_status'] ?? null,
        ':race' => $_POST['race'] ?? null,
        ':religion' => $_POST['religion'] ?? null,
        ':nationality' => $_POST['nationality'] ?? null,
        ':occupation' => $_POST['occupation'] ?? null,
        ':exact_duties' => $_POST['exact_duties'] ?? null,
        ':nature_of_business' => $_POST['nature_of_business'] ?? null,
        ':salary_yearly' => $_POST['salary_yearly'] ?? null,
        ':company_name' => $_POST['company_name'] ?? null,
        ':company_address' => $_POST['company_address'] ?? null,
        ':weight' => $_POST['weight'] ?? null,
        ':height' => $_POST['height'] ?? null,
        ':smoker' => $_POST['smoker'] ?? null,
        ':hospital_admission_history' => $_POST['hospital_admission_history'] ?? null,
        ':client_id' => $clientId
    ]);

    // Update agent-client relationship if agent_client table exists
    $tableCheck = $pdo->query("SHOW TABLES LIKE 'agent_client'");
    if ($tableCheck->rowCount() > 0) {
        $agentId = $_POST['agent_id'] ?? null;

        // Check if assignment already exists
        $stmt = $pdo->prepare("SELECT * FROM agent_client WHERE client_id = ?");
        $stmt->execute([$clientId]);
        $existing = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!empty($agentId)) {
            if ($existing) {
                $stmt = $pdo->prepare("UPDATE agent_client SET agent_id = ? WHERE client_id = ?");
                $stmt->execute([$agentId, $clientId]);
            } else {
                $stmt = $pdo->prepare("INSERT INTO agent_client (agent_id, client_id) VALUES (?, ?)");
                $stmt->execute([$agentId, $clientId]);
            }
        } else {
            if ($existing) {
                $stmt = $pdo->prepare("DELETE FROM agent_client WHERE client_id = ?");
                $stmt->execute([$clientId]);
            }
        }
    }

    $pdo->commit();

    // Log activity
    logActivity(123, 'Edited client', "Edited client ID {$clientId}");

    echo json_encode([
        'success' => true,
        'message' => 'Client updated successfully'
    ]);
} catch (Exception $e) {
    $pdo->rollBack();
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error updating client: ' . $e->getMessage()
    ]);
}
