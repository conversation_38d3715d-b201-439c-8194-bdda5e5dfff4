<?php
session_start();
require_once 'config.php';
require_once 'policies.php';

// Return JSON
header('Content-Type: application/json');

// Check login
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized'
    ]);
    exit;
}

// Read input
$data = json_decode(file_get_contents('php://input'), true);
if (!$data) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Invalid JSON payload'
    ]);
    exit;
}

// Validate required policy fields
$policyErrors = validatePolicyPayload($data);
if (!empty($policyErrors)) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Validation failed',
        'errors' => $policyErrors
    ]);
    exit;
}

// Insert policy
$policyData = [
    'client_id' => $data['client_id'],
    'agent_id' => $data['agent_id'],
    'plan_type' => $data['plan_type'],
    'policy_id' => $data['policy_id'],
    'basic_plan_rider' => $data['basic_plan_rider'],
    'sum_covered' => $data['sum_covered'],
    'coverage_term' => $data['coverage_term'],
    'contribution' => $data['contribution'],
    'status' => 'Pending',
    'start_date' => $data['start_date'],
    'end_date' => $data['end_date']
];

$policyId = pm_addPolicy($policyData);

if (!$policyId) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to create policy'
    ]);
    exit;
}

// Insert beneficiary if present
if (!empty($data['beneficiary']) && !empty($data['beneficiary']['name']) && !empty($data['beneficiary']['ic_number'])) {
    try {
        $beneficiary = $data['beneficiary'];

        $sql = "INSERT INTO beneficiaries (
            policy_id, name, ic_number, relationship, date_of_birth,
            phone_number, email, address, percentage
        ) VALUES (
            :policy_id, :name, :ic_number, :relationship, :date_of_birth,
            :phone_number, :email, :address, :percentage
        )";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            ':policy_id' => $data['policy_id'],
            ':name' => $beneficiary['name'],
            ':ic_number' => $beneficiary['ic_number'],
            ':relationship' => $beneficiary['relationship'],
            ':date_of_birth' => $beneficiary['date_of_birth'] ?: null,
            ':phone_number' => $beneficiary['phone_number'],
            ':email' => $beneficiary['email'],
            ':address' => $beneficiary['address'],
            ':percentage' => $beneficiary['percentage'] ?: 100
        ]);
    } catch (Exception $e) {
        error_log("Error adding beneficiary: " . $e->getMessage());
        // Continue without failing the whole request
    }
}

// Return success
echo json_encode([
    'success' => true,
    'message' => 'Policy added successfully',
    'policy_id' => $policyId
]);

// Helper validation function
function validatePolicyPayload($policy) {
    $errors = [];
    if (empty($policy['client_id'])) $errors[] = 'client_id is required';
    if (empty($policy['agent_id'])) $errors[] = 'agent_id is required';
    if (empty($policy['plan_type'])) $errors[] = 'plan_type is required';
    if (empty($policy['policy_id'])) $errors[] = 'policy_id is required';
    if (empty($policy['basic_plan_rider'])) $errors[] = 'basic_plan_rider is required';
    if (!isset($policy['sum_covered'])) $errors[] = 'sum_covered is required';
    if (empty($policy['coverage_term'])) $errors[] = 'coverage_term is required';
    if (!isset($policy['contribution'])) $errors[] = 'contribution is required';
    if (empty($policy['start_date'])) $errors[] = 'start_date is required';
    if (empty($policy['end_date'])) $errors[] = 'end_date is required';
    return $errors;
}
?>
