<?php
// api/add_admin.php

ini_set('display_errors', 1);
error_reporting(E_ALL);

header('Content-Type: application/json');

require_once '../config.php';

// Dapatkan JSON input dari Flutter
$rawInput = file_get_contents('php://input');
$data = json_decode($rawInput, true);

// Validate input
if (!isset($data['username']) || !isset($data['password']) || !isset($data['email'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Incomplete data sent.'
    ]);
    exit;
}

$username = $data['username'];
$password = $data['password'];
$email = $data['email'];

// Check if user exists
$stmt = $pdo->prepare("SELECT * FROM users WHERE username = ? OR email = ?");
$stmt->execute([$username, $email]);
$existingUser = $stmt->fetch();

if ($existingUser) {
    echo json_encode([
        'success' => false,
        'message' => 'User already exists.'
    ]);
    exit;
} else {
    // Hash password
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    
    $stmt = $pdo->prepare("INSERT INTO users (username, email, password, created_at) VALUES (?, ?, ?, NOW())");
    $result = $stmt->execute([$username, $email, $hashedPassword]);

    if ($result) {
        $userId = $pdo->lastInsertId();
        echo json_encode([
            'success' => true,
            'message' => 'Admin user created.',
            'user_id' => $userId,
            'username' => $username,
            'email' => $email
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to create user.'
        ]);
    }
}
